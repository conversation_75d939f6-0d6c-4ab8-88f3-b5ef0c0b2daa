'use client';

import React from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Switch } from '~/components/ui/switch';
import { ScrollArea } from '~/components/ui/scroll-area';

import {
  User,
  Users,
  Mic,
  Settings,
  Zap,
  Clock,
  FileText,
  Sparkles,
  Volume2
} from 'lucide-react';
import type { StudioSettingsPanelProps } from '../types';

export const StudioSettingsPanel: React.FC<StudioSettingsPanelProps> = ({
  languages,
  roles,
  selectedLanguage,
  selectedRole,
  selectedModel,
  selectedM<PERSON>,
  onLanguageChange,
  onRoleChange,
  onModelChange,
  onModeChange
}) => {
  return (
    <div className="w-full md:w-80 bg-background border-l h-full">
      <ScrollArea className="h-full">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Voice Settings</h2>
            <p className="text-sm text-muted-foreground">
              Configure your voice generation preferences
            </p>
          </div>

          <Separator />

          {/* Generation Mode */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Generation Mode
              </CardTitle>
              <CardDescription>
                Choose how you want to generate speech
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <RadioGroup value={selectedMode} onValueChange={onModeChange}>
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent">
                  <RadioGroupItem value="single" id="single" />
                  <Label htmlFor="single" className="flex items-center cursor-pointer flex-1">
                    <User className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div className="space-y-1">
                      <div className="font-medium">Single Speaker</div>
                      <div className="text-xs text-muted-foreground">Generate speech with one voice</div>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent">
                  <RadioGroupItem value="multi" id="multi" />
                  <Label htmlFor="multi" className="flex items-center cursor-pointer flex-1">
                    <Users className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div className="space-y-1">
                      <div className="font-medium">Multi-Speaker</div>
                      <div className="text-xs text-muted-foreground">Create dialogue with multiple voices</div>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent">
                  <RadioGroupItem value="batch" id="batch" />
                  <Label htmlFor="batch" className="flex items-center cursor-pointer flex-1">
                    <FileText className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div className="space-y-1">
                      <div className="font-medium">Batch Processing</div>
                      <div className="text-xs text-muted-foreground">Process multiple texts at once</div>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

      {/* 模式选择 */}
      <div>
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
          Generation Mode
        </label>
        <RadioGroup value={selectedMode} onValueChange={onModeChange}>
          <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50">
            <RadioGroupItem value="single" id="single" />
            <Label htmlFor="single" className="flex items-center cursor-pointer flex-1">
              <User className="h-4 w-4 mr-2" />
              <div>
                <div className="font-medium">Single Speaker</div>
                <div className="text-xs text-gray-500">Generate speech with one voice</div>
              </div>
            </Label>
          </div>
          <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50">
            <RadioGroupItem value="multi" id="multi" />
            <Label htmlFor="multi" className="flex items-center cursor-pointer flex-1">
              <Users className="h-4 w-4 mr-2" />
              <div>
                <div className="font-medium">Multi-Speaker</div>
                <div className="text-xs text-gray-500">Create dialogue with multiple voices</div>
              </div>
            </Label>
          </div>
          <div className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50">
            <RadioGroupItem value="batch" id="batch" />
            <Label htmlFor="batch" className="flex items-center cursor-pointer flex-1">
              <FileText className="h-4 w-4 mr-2" />
              <div>
                <div className="font-medium">Batch Processing</div>
                <div className="text-xs text-gray-500">Process multiple texts at once</div>
              </div>
            </Label>
          </div>
        </RadioGroup>
      </div>

          {/* Language Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                Language & Voice
              </CardTitle>
              <CardDescription>
                Select the language and voice for generation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Language Selection */}
              <div className="space-y-2">
                <Label htmlFor="language-select" className="text-sm font-medium">
                  Language
                </Label>
                <Select value={selectedLanguage} onValueChange={onLanguageChange}>
                  <SelectTrigger id="language-select">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {languages?.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex flex-col">
                            <span className="font-medium">{lang.name}</span>
                            {lang.nativeName && lang.nativeName !== lang.name && (
                              <span className="text-xs text-muted-foreground">{lang.nativeName}</span>
                            )}
                          </div>
                          <div className="flex items-center space-x-1 ml-2">
                            {lang.region && (
                              <Badge variant="secondary" className="text-xs">
                                {lang.region}
                              </Badge>
                            )}
                            {(lang as any)._aggr_count_roleSupports > 0 && (
                              <Badge variant="outline" className="text-xs">
                                {(lang as any)._aggr_count_roleSupports} voices
                              </Badge>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Voice Selection */}
              <div className="space-y-2">
                <Label htmlFor="voice-select" className="text-sm font-medium">
                  Voice
                </Label>
                <Select
                  value={selectedRole?.id || ''}
                  onValueChange={(value) => {
                    const role = roles?.find((r: any) => r.id === value);
                    onRoleChange(role || null);
                    if (role?.modelMappings?.[0]) {
                      onModelChange(role.modelMappings[0]);
                    }
                  }}
                >
                  <SelectTrigger id="voice-select">
                    <SelectValue placeholder="Select voice" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles?.map((role: any) => (
                      <SelectItem key={role.id} value={role.id}>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center space-x-2">
                            <Mic className="h-4 w-4 text-muted-foreground" />
                            <div className="flex flex-col">
                              <span className="font-medium">{role.nameEn || role.name}</span>
                              {role.description && (
                                <span className="text-xs text-muted-foreground">{role.description}</span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-1 ml-2">
                            {role.modelMappings?.length > 1 && (
                              <Badge variant="outline" className="text-xs">
                                {role.modelMappings.length} models
                              </Badge>
                            )}
                            {role.gender && (
                              <Badge variant="secondary" className="text-xs">
                                {role.gender}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Model Quality Selection */}
          {selectedRole?.modelMappings && selectedRole.modelMappings.length > 1 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Model Quality
                </CardTitle>
                <CardDescription>
                  Choose between speed and quality
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {selectedRole.modelMappings.map((mapping: any) => (
                  <Button
                    key={mapping.id}
                    variant={selectedModel?.id === mapping.id ? 'default' : 'outline'}
                    onClick={() => onModelChange(mapping)}
                    className="w-full justify-start text-left h-auto p-4"
                  >
                    <div className="flex items-center space-x-3">
                      {mapping.model.name.includes('hd') || mapping.model.name.includes('pro') ? (
                        <Clock className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Zap className="h-4 w-4 text-muted-foreground" />
                      )}
                      <div className="text-left">
                        <div className="font-medium">{mapping.model.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {mapping.model.name.includes('hd') || mapping.model.name.includes('pro')
                            ? 'High Quality • Slower generation'
                            : 'Fast Generation • Standard quality'
                          }
                        </div>
                      </div>
                    </div>
                  </Button>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Advanced Settings */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Advanced Settings
              </CardTitle>
              <CardDescription>
                Fine-tune your generation preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Auto-play generated audio</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically play audio when generation completes
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Save to history</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically save generated audio to project history
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              {/* Current Model Info */}
              {selectedModel && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Current Model</Label>
                    <div className="rounded-lg border p-3 bg-muted/50">
                      <div className="font-medium text-sm">{selectedModel.model.name}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Provider: {selectedModel.model.provider.name}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Type: {selectedModel.model.modelType}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};
