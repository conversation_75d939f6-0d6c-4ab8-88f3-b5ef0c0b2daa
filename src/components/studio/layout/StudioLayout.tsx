'use client';

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Button } from '~/components/ui/button';
import { PanelLeft, PanelRight, FolderOpen, Save } from 'lucide-react';
import {
  SidebarProvider,
  SidebarInset,
  useSidebar
} from '~/components/ui/sidebar';
import { StudioSidebar } from './StudioSidebar';
import { StudioSettingsPanel } from './StudioSettingsPanel';
import { TextEditor } from '../features/TextEditor';
import { AudioPlayer } from '../features/AudioPlayer';
import { MultiSpeakerEditor } from '../features/MultiSpeakerEditor';
import { BatchProcessor } from '../features/BatchProcessor';
// import {
//   useDebounce,
//   usePerformanceProfiler,
//   useMemoryMonitor
// } from '../utils/PerformanceOptimizations';
import { ErrorDisplay } from '../ui/ErrorDisplay';
import { LoadingOverlay } from '../ui/LoadingOverlay';

import { ProjectDialog } from '../ui/ProjectDialog';
import { UserSettingsPanel } from '../ui/UserSettingsPanel';
import { useStudio } from '../context/StudioContext';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

import { api } from '~/trpc/react';
import type { StudioLayoutProps } from '../types';

// 侧边栏切换按钮组件
const SidebarToggleButton = () => {
  const { toggleSidebar } = useSidebar();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleSidebar}
      className="h-8 w-8 p-0"
    >
      <PanelLeft className="h-4 w-4" />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  );
};

export const StudioLayout: React.FC<StudioLayoutProps> = ({ children }) => {
  // 性能监控
  // usePerformanceProfiler('StudioLayout');
  // const memoryInfo = useMemoryMonitor();
  // const { handleModeHover } = useSmartPreload();

  const [isMobile, setIsMobile] = useState(false);
  const [settingsPanelOpen, setSettingsPanelOpen] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [userSettingsOpen, setUserSettingsOpen] = useState(false);
  const textInputRef = useRef<HTMLTextAreaElement>(null);

  const {
    sidebarCollapsed,
    setSidebarCollapsed,
    selectedMode,
    setSelectedMode,
    styleInstructions,
    setStyleInstructions,
    inputText,
    setInputText,
    selectedLanguage,
    setSelectedLanguage,
    selectedRole,
    setSelectedRole,
    selectedModel,
    setSelectedModel,
    isGenerating,
    audioState,

    handleGenerate,
    togglePlayPause,
    stopAudio,
    seekAudio,
    changeVolume,
    error,
    clearError,
    retryGeneration,
    generateMultiSpeaker,
    generateVoice,
    // 项目管理
    currentProject,
    projects,
    isProjectDialogOpen,
    isSaving,
    createProject,
    saveProject,
    loadProject,
    deleteProject,
    duplicateProject,
    openProjectDialog,
    closeProjectDialog,
    isCreating,
    isDeleting,
    isDuplicating
  } = useStudio();

  // 防抖优化
  // const debouncedInputText = useDebounce(inputText, 300);
  // const debouncedStyleInstructions = useDebounce(styleInstructions, 300);

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // 在移动端自动折叠侧边栏
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [setSidebarCollapsed]);

  // 保存项目函数
  const handleSaveProject = useCallback(async () => {
    try {
      const content = { inputText, styleInstructions };
      const settings = {
        selectedLanguage,
        selectedRoleId: selectedRole?.id,
        selectedModelId: selectedModel?.id,
        selectedMode,
      };

      await saveProject(content, settings);
    } catch (error) {
      console.error('Failed to save project:', error);
    }
  }, [inputText, styleInstructions, selectedLanguage, selectedRole, selectedModel, selectedMode, saveProject]);

  // 聚焦文本输入框
  const handleFocusTextInput = useCallback(() => {
    textInputRef.current?.focus();
  }, []);

  // 键盘快捷键配置
  const { shortcuts } = useKeyboardShortcuts({
    onGenerate: handleGenerate,
    onPlayPause: togglePlayPause,
    onStop: stopAudio,
    onSave: handleSaveProject,
    onToggleSidebar: () => setSidebarCollapsed(!sidebarCollapsed),
    onClearError: clearError,
    onFocusTextInput: handleFocusTextInput,
  });

  // API查询
  const { data: languages } = api.language.getAll.useQuery();
  const { data: rolesData = [] } = api.tts.getRolesByLanguage.useQuery(
    { languageCode: selectedLanguage, limit: 50 },
    { enabled: !!selectedLanguage }
  );
  const roles = Array.isArray(rolesData) ? rolesData : [];



  return (
    <SidebarProvider>
      <StudioSidebar
        onOpenUserSettings={() => {
          setUserSettingsOpen(true);
        }}
      />

      {/* 用户设置面板 */}
      {userSettingsOpen && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setUserSettingsOpen(false)}
          />
          {/* 用户设置面板 */}
          <div className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-50 transform transition-transform duration-300 ease-in-out"
               style={{
                 left: '256px', // Fixed position next to sidebar
                 transform: userSettingsOpen ? 'translateX(0)' : 'translateX(-100%)'
               }}>
            <UserSettingsPanel />
          </div>
        </>
      )}

      <SidebarInset>
        {/* Main content area with proper three-column layout */}
        <div className="flex flex-1 min-h-0">
          {/* 中间工作区 - 主要内容 */}
          <div className="flex-1 flex flex-col min-w-0 relative overflow-hidden">

              {/* 中间内容区顶部工具条 */}
              <div className="flex items-center justify-between h-14 px-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex items-center gap-2">
                  {/* 左侧边栏切换按钮 */}
                  <SidebarToggleButton />
                  <div className="h-6 w-px bg-border" />
                  <h1 className="text-lg font-semibold truncate">
                    {currentProject?.name || 'Untitled Project'}
                  </h1>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openProjectDialog}
                    className="hidden md:flex"
                  >
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Projects
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSaveProject}
                    disabled={isSaving}
                    className="hidden md:flex"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </Button>
                  {/* 右侧面板切换按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      if (isMobile) {
                        setSettingsPanelOpen(!settingsPanelOpen);
                      } else {
                        setRightPanelCollapsed(!rightPanelCollapsed);
                      }
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <PanelRight className="h-4 w-4" />
                    <span className="sr-only">Toggle Right Panel</span>
                  </Button>
                </div>
              </div>

              {/* 内容区域 */}
              <div className="flex-1 p-4 md:p-6 overflow-hidden">
              {/* 加载覆盖层 */}
              <LoadingOverlay
                isLoading={isGenerating}
                message="Generating voice..."
              />

              {/* 错误显示 */}
              <ErrorDisplay
                error={error}
                onClear={clearError}
                onRetry={retryGeneration}
                showRetry={!!selectedRole && !!inputText.trim()}
              />

              {/* 编辑器区域 - 可滚动 */}
              <div className="flex-1 flex flex-col min-h-0 space-y-4 md:space-y-6">
                {selectedMode === 'single' ? (
                  <TextEditor
                    ref={textInputRef}
                    styleInstructions={styleInstructions}
                    inputText={inputText}
                    onStyleInstructionsChange={setStyleInstructions}
                    onInputTextChange={setInputText}
                  />
                ) : selectedMode === 'multi' ? (
                  <MultiSpeakerEditor
                    roles={roles || []}
                    onGenerate={generateMultiSpeaker as any}
                    isGenerating={isGenerating}
                  />
                ) : (
                  <BatchProcessor
                    roles={roles || []}
                    selectedRole={selectedRole}
                    onGenerate={async (text: string) => {
                      const result = await generateVoice.mutateAsync({
                        text,
                        roleId: selectedRole?.id || '',
                        modelMappingId: selectedModel?.id || '',
                      });
                      return { audioUrl: result.audioUrl };
                    }}
                    isGenerating={isGenerating}
                  />
                )}

                {/* 音频播放器 */}
                <AudioPlayer
                  audioState={audioState}
                  onTogglePlayPause={togglePlayPause}
                  onStop={stopAudio}
                  onVolumeChange={changeVolume}
                  onSeek={seekAudio}
                />

                {/* 底部操作栏 */}
                <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700 space-y-3 md:space-y-0">
                  <div className="flex flex-col md:flex-row md:items-center space-y-1 md:space-y-0 md:space-x-4">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Campfire story</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 hidden md:block">Use Gemini to read a disclaimer, really fast</span>
                  </div>
                  <Button
                    onClick={handleGenerate}
                    disabled={!selectedRole || !inputText.trim() || isGenerating}
                    className="px-6 w-full md:w-auto"
                    size={isMobile ? "lg" : "default"}
                  >
                    {isGenerating ? 'Generating...' : 'Run'}
                  </Button>
                </div>

                {/* 自定义内容 */}
                {children}
              </div>
            </div>

            {/* 右侧设置面板 - 可收缩 */}
            <div className={`${
              isMobile
                ? `fixed right-0 top-0 h-full w-80 z-50 transform transition-transform duration-300 ${
                    settingsPanelOpen ? 'translate-x-0' : 'translate-x-full'
                  }`
                : rightPanelCollapsed
                  ? 'w-12 border-l border-border bg-background'
                  : 'w-80 border-l border-border bg-background'
            } flex-shrink-0 transition-all duration-300 ease-in-out`}>
              {/* 移动端遮罩层 */}
              {isMobile && settingsPanelOpen && (
                <div
                  className="fixed inset-0 bg-black bg-opacity-50 z-40"
                  onClick={() => setSettingsPanelOpen(false)}
                />
              )}
              <div className="relative z-50">
                {!isMobile && rightPanelCollapsed ? (
                  // 收缩状态 - 空白区域
                  <div className="w-full h-full"></div>
                ) : (
                  // 展开状态 - 显示完整面板
                  <StudioSettingsPanel
                    languages={languages || []}
                    roles={roles || []}
                    selectedLanguage={selectedLanguage}
                    selectedRole={selectedRole}
                    selectedModel={selectedModel}
                    selectedMode={selectedMode}
                    onLanguageChange={setSelectedLanguage}
                    onRoleChange={setSelectedRole}
                    onModelChange={setSelectedModel}
                    onModeChange={setSelectedMode}
                  />
                )}
              </div>
            </div>
        </div>

        {/* Project Dialog */}
          <ProjectDialog
            open={isProjectDialogOpen}
            onOpenChange={closeProjectDialog}
            projects={projects}
            currentProject={currentProject}
            onCreateProject={createProject}
            onLoadProject={loadProject}
            onDeleteProject={deleteProject}
            onDuplicateProject={duplicateProject}
            isCreating={isCreating}
            isDeleting={isDeleting}
            isDuplicating={isDuplicating}
          />
      </SidebarInset>
    </SidebarProvider>
  );
};
