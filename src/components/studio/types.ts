// Studio组件的类型定义

export interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  url: string | null;
  volume: number;
  isLoading: boolean;
  multiSpeakerResults?: Array<{
    segmentId: string;
    speakerId: string;
    audioUrl: string;
    text: string;
    order: number;
  }>;
}

export interface VoiceRole {
  id: string;
  name: string;
  nameEn?: string;
  nameZh?: string;
  voiceName: string;
  description?: string;
  avatarUrl?: string;
  isActive: boolean;
  languageSupports?: LanguageSupport[];
  modelMappings?: ModelMapping[];
}

export interface LanguageSupport {
  id: string;
  languageCode: string;
  quality: string;
  isDefault: boolean;
  language: Language;
}

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  region: string;
  isActive: boolean;
  roleCount?: number;
}

export interface ModelMapping {
  id: string;
  priority: number;
  isDefault: boolean;
  isActive: boolean;
  model: Model;
}

export interface Model {
  id: string;
  name: string;
  displayName?: string;
  modelType: string;
  isActive: boolean;
  provider: ModelProvider;
}

export interface ModelProvider {
  id: string;
  name: string;
  slug: string;
  isActive: boolean;
}

export interface StudioState {
  // UI状态
  sidebarCollapsed: boolean;
  selectedMode: 'single' | 'multi' | 'batch';
  
  // 内容状态
  styleInstructions: string;
  inputText: string;
  
  // 选择状态
  selectedLanguage: string;
  selectedRole: VoiceRole | null;
  selectedModel: ModelMapping | null;
  
  // 生成状态
  isGenerating: boolean;
  audioState: AudioState;
  error: string | null;
}

export interface StudioActions {
  // UI操作
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSelectedMode: (mode: 'single' | 'multi' | 'batch') => void;
  
  // 内容操作
  setStyleInstructions: (instructions: string) => void;
  setInputText: (text: string) => void;
  
  // 选择操作
  setSelectedLanguage: (language: string) => void;
  setSelectedRole: (role: VoiceRole | null) => void;
  setSelectedModel: (model: ModelMapping | null) => void;
  
  // 生成操作
  setIsGenerating: (generating: boolean) => void;
  setAudioState: (state: AudioState | ((prev: AudioState) => AudioState)) => void;
  
  // 业务操作
  handleGenerate: () => Promise<void>;
  togglePlayPause: () => void;
  stopAudio: () => void;
  seekAudio: (time: number) => void;
  changeVolume: (volume: number) => void;
  clearError: () => void;
  retryGeneration: () => void;
  generateMultiSpeaker: (segments: any[], speakers: any[]) => Promise<any[]>;
  generateVoice: any; // tRPC mutation

  // 项目管理 (从 useProjectManager 继承)
  currentProject: any;
  projects: any[];
  isProjectDialogOpen: boolean;
  isSaving: boolean;
  createProject: (name: string, description?: string, content?: any, settings?: any) => Promise<any>;
  saveProject: (content: any, settings: any, projectName?: string) => Promise<any>;
  loadProject: (projectId: string) => Promise<any>;
  deleteProject: (projectId: string) => Promise<void>;
  duplicateProject: (projectId: string, newName?: string) => Promise<any>;
  addToHistory: (action: string, changes?: Record<string, any>, audioUrl?: string) => Promise<void>;
  autoSave: (content: any, settings: any) => Promise<void>;
  newProject: () => void;
  openProjectDialog: () => void;
  closeProjectDialog: () => void;
  setCurrentProject: (project: any) => void;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isDuplicating: boolean;
}

export interface StudioContextType extends StudioState, StudioActions {}

// 组件Props类型
export interface StudioSidebarProps {
  onOpenUserSettings?: () => void;
}

export interface StudioSettingsPanelProps {
  languages: Language[];
  roles: VoiceRole[];
  selectedLanguage: string;
  selectedRole: VoiceRole | null;
  selectedModel: ModelMapping | null;
  selectedMode: 'single' | 'multi' | 'batch';
  onLanguageChange: (language: string) => void;
  onRoleChange: (role: VoiceRole | null) => void;
  onModelChange: (model: ModelMapping | null) => void;
  onModeChange: (mode: 'single' | 'multi' | 'batch') => void;
}

export interface AudioPlayerProps {
  audioState: AudioState;
  onTogglePlayPause: () => void;
  onStop: () => void;
  onVolumeChange: (volume: number) => void;
  onSeek: (time: number) => void;
}

export interface TextEditorProps {
  styleInstructions: string;
  inputText: string;
  onStyleInstructionsChange: (instructions: string) => void;
  onInputTextChange: (text: string) => void;
}

export interface StudioLayoutProps {
  children?: React.ReactNode;
}
