'use client';

import React, { forwardRef, useEffect, useRef, useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '~/components/ui/card';
import { Textarea } from '~/components/ui/textarea';
import { Label } from '~/components/ui/label';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Separator } from '~/components/ui/separator';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  FileText,
  Type,
  Copy,
  Trash2,
  RotateCcw,
  Sparkles,
  AlignLeft
} from 'lucide-react';
import type { TextEditorProps } from '../types';

export const TextEditor = forwardRef<HTMLTextAreaElement, TextEditorProps>(({
  styleInstructions,
  inputText,
  onStyleInstructionsChange,
  onInputTextChange
}, ref) => {
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [lineCount, setLineCount] = useState(1);
  const styleTextareaRef = useRef<HTMLTextAreaElement>(null);
  const mainTextareaRef = useRef<HTMLTextAreaElement>(null);

  // 计算文本统计
  const updateTextStats = useCallback((text: string) => {
    const chars = text.length;
    const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
    const lines = text.split('\n').length;

    setCharCount(chars);
    setWordCount(words);
    setLineCount(lines);
  }, []);

  // 当输入文本变化时更新统计
  useEffect(() => {
    updateTextStats(inputText);
  }, [inputText, updateTextStats]);

  // 自动调整textarea高度
  const autoResize = useCallback((textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  // 处理文本变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    onInputTextChange(value);
    autoResize(e.target);
  }, [onInputTextChange, autoResize]);

  const handleStyleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    onStyleInstructionsChange(value);
    autoResize(e.target);
  }, [onStyleInstructionsChange, autoResize]);

  // 清空文本
  const clearText = useCallback(() => {
    onInputTextChange('');
    if (mainTextareaRef.current) {
      mainTextareaRef.current.focus();
    }
  }, [onInputTextChange]);

  // 复制文本
  const copyText = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(inputText);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  }, [inputText]);

  // 重置样式说明
  const resetStyleInstructions = useCallback(() => {
    onStyleInstructionsChange('Read aloud in a warm and friendly tone:');
  }, [onStyleInstructionsChange]);

  // 键盘快捷键处理
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Ctrl+Enter 可以用于触发生成（由父组件处理）
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      // 这里可以触发生成事件，但需要父组件传递回调
    }

    // Tab键插入缩进而不是失去焦点
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = e.target as HTMLTextAreaElement;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const value = textarea.value;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);

      onInputTextChange(newValue);

      // 设置光标位置
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }
  }, [onInputTextChange]);

  return (
    <div className="space-y-6">
      {/* 样式说明 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="text-base flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Style Instructions
              </CardTitle>
              <CardDescription>
                Describe how you want the text to be read aloud
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetStyleInstructions}
              className="h-8 w-8 p-0"
            >
              <RotateCcw className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Textarea
            ref={styleTextareaRef}
            value={styleInstructions}
            onChange={handleStyleChange}
            placeholder="Enter style instructions..."
            className="min-h-[60px] resize-none"
          />
        </CardContent>
      </Card>

      {/* 文本输入 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="text-base flex items-center gap-2">
                <AlignLeft className="h-4 w-4" />
                Text Content
              </CardTitle>
              <CardDescription>
                Enter the text you want to convert to speech
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={copyText}
                disabled={!inputText}
                className="h-8 w-8 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearText}
                disabled={!inputText}
                className="h-8 w-8 p-0"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Textarea
            ref={ref || mainTextareaRef}
            value={inputText}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Start writing or paste text here to generate speech (Ctrl+/ to focus, Ctrl+Enter to generate)"
            className="min-h-[200px] resize-none"
          />
        </CardContent>
        <CardFooter className="flex items-center justify-between pt-3">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Type className="h-3 w-3" />
              <span>{charCount} characters</span>
            </div>
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <span>{wordCount} words</span>
            </div>
            <div className="flex items-center gap-1">
              <AlignLeft className="h-3 w-3" />
              <span>{lineCount} lines</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {inputText.length > 1000 && (
              <Badge variant="secondary" className="text-xs">
                Long text
              </Badge>
            )}
            {inputText.length > 5000 && (
              <Badge variant="destructive" className="text-xs">
                Very long
              </Badge>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
});

TextEditor.displayName = 'TextEditor';
