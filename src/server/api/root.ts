import { createCallerFactory, create<PERSON><PERSON><PERSON>out<PERSON> } from "~/server/api/trpc";
import { postRouter } from "~/server/api/routers/post";
import { ttsRouter } from "~/server/api/routers/tts";
import { r2Router } from "~/server/api/routers/r2";
import { userRouter } from "~/server/api/routers/user";
import { creditPackageRouter } from "~/server/api/routers/creditPackage";
import { orderRouter } from "~/server/api/routers/order";
import { statsRouter } from "~/server/api/routers/stats";
import { voiceRoleRouter } from "~/server/api/routers/voiceRole";
import { tokenUsageRouter } from "~/server/api/routers/tokenUsage";

import { unifiedModelRouter } from "~/server/api/routers/unifiedModel";
import { systemSettingsRouter } from "~/server/api/routers/systemSettings";
import { languageRouter } from "~/server/api/routers/language";
import { voiceSampleRouter } from "~/server/api/routers/voiceSample";
import { providerRouter } from "~/server/api/routers/provider";
import { modelRouter } from "~/server/api/routers/model";
import { creditServiceRouter } from "~/server/api/routers/creditService";
import { customPricingRouter } from "~/server/api/routers/customPricing";
import { projectRouter } from "~/server/api/routers/project";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  tts: ttsRouter,
  r2: r2Router,
  user: userRouter,
  creditPackage: creditPackageRouter,
  order: orderRouter,
  stats: statsRouter,
  voiceRole: voiceRoleRouter,
  tokenUsage: tokenUsageRouter,


  unifiedModel: unifiedModelRouter,
  systemSettings: systemSettingsRouter,
  language: languageRouter,
  voiceSample: voiceSampleRouter,
  provider: providerRouter,
  model: modelRouter,
  creditService: creditServiceRouter,
  customPricing: customPricingRouter,
  project: projectRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
