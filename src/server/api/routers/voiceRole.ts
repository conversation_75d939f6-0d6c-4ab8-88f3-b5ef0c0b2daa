import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

// 管理员权限中间件
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.session?.user?.id) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const user = await ctx.db.user.findUnique({
    where: { id: ctx.session.user.id },
    select: { role: true },
  });

  if (user?.role !== "ADMIN") {
    throw new TRPCError({ code: "FORBIDDEN" });
  }

  return next({
    ctx: {
      ...ctx,
      user: ctx.session.user,
    },
  });
});

export const voiceRoleRouter = createTRPCRouter({
  // 公开的语音角色列表（用于前端用户选择）
  getPublicVoiceRoles: publicProcedure
    .input(
      z.object({
        search: z.string().optional(),
        gender: z.enum(["MALE", "FEMALE", "NEUTRAL"]).optional(),
        language: z.string().optional(),
        modelId: z.string().optional(), // 可选的模型ID，用于过滤支持该模型的角色
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(10),
      })
    )
    .query(async ({ ctx, input }) => {
      const { search, gender, language, modelId, page, limit } = input;
      const skip = (page - 1) * limit;

      const where: any = {
        isActive: true, // 只返回激活的角色
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(gender && { genderEn: gender }),
        ...(language && {
          languageSupports: {
            some: {
              language: { code: language },
            },
          },
        }),
        // 如果提供了modelId，只返回支持该模型的角色
        ...(modelId && {
          modelMappings: {
            some: {
              modelId: modelId,
              isActive: true
            }
          }
        })
      };

      const [roles, total] = await Promise.all([
        ctx.db.ttsRole.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: "desc" },
          select: {
            id: true,
            name: true,
            description: true,
            genderEn: true,
            genderZh: true,
            avatarUrl: true,
            isActive: true,
            languageSupports: {
              select: {
                language: {
                  select: {
                    code: true,
                    name: true,
                  },
                },
              },
            },
            // 包含模型关联信息（仅在按模型过滤时返回）
            modelMappings: modelId ? {
              where: {
                modelId: modelId,
                isActive: true
              },
              select: {
                isDefault: true,
                priority: true,
                model: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true
                  }
                }
              }
            } : false,
          },
        }),
        ctx.db.ttsRole.count({ where }),
      ]);

      return {
        roles,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 管理员获取语音角色列表
  getVoiceRoles: adminProcedure
    .input(
      z.object({
        search: z.string().optional(),
        gender: z.enum(["Male", "Female"]).optional(),
        language: z.string().optional(),
        region: z.string().optional(),
        provider: z.string().optional(),
        style: z.string().optional(),
        isActive: z.boolean().optional(),
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(100).default(20),
        sortBy: z.enum(["name", "createdAt", "updatedAt"]).default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).default("desc"),
        includeModelMappings: z.boolean().default(false), // 是否包含模型关联信息
      })
    )
    .query(async ({ ctx, input }) => {
      const {
        search,
        gender,
        language,
        region,
        provider,
        style,
        isActive,
        page,
        limit,
        sortBy,
        sortOrder,
        includeModelMappings
      } = input;
      const skip = (page - 1) * limit;

      const where: any = {
        ...(search && {
          OR: [
            { name: { contains: search, mode: "insensitive" as const } },
            { nameEn: { contains: search, mode: "insensitive" as const } },
            { nameZh: { contains: search, mode: "insensitive" as const } },
            { description: { contains: search, mode: "insensitive" as const } },
            { descriptionEn: { contains: search, mode: "insensitive" as const } },
            { descriptionZh: { contains: search, mode: "insensitive" as const } },
            { voiceName: { contains: search, mode: "insensitive" as const } },
          ],
        }),
        ...(gender && { genderEn: gender }),
        ...(isActive !== undefined && { isActive }),
        ...(language && {
          languageSupports: {
            some: {
              languageCode: language,
            },
          },
        }),
        ...(region && {
          languageSupports: {
            some: {
              language: {
                region: region,
              },
            },
          },
        }),
        ...(provider && {
          modelMappings: {
            some: {
              model: {
                provider: {
                  slug: provider,
                },
              },
            },
          },
        }),
        ...(style && {
          OR: [
            { styles: { has: style } },
            { stylesEn: { has: style } },
            { stylesZh: { has: style } },
          ],
        }),
      };

      const [roles, total] = await Promise.all([
        ctx.db.ttsRole.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            languageSupports: {
              include: {
                language: true,
              },
            },
            ...(includeModelMappings && {
              modelMappings: {
                where: { isActive: true },
                include: {
                  model: {
                    include: {
                      provider: true,
                    }
                  }
                },
                orderBy: [
                  { isDefault: "desc" },
                  { priority: "asc" }
                ]
              }
            })
          },
        }),
        ctx.db.ttsRole.count({ where }),
      ]);

      return {
        roles,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取语音角色统计信息
  getVoiceRoleStats: adminProcedure
    .query(async ({ ctx }) => {
      const [
        totalRoles,
        activeRoles,
        genderStats,
        languageStats,
        providerStats,
        styleStats,
      ] = await Promise.all([
        // 总角色数
        ctx.db.ttsRole.count(),

        // 活跃角色数
        ctx.db.ttsRole.count({ where: { isActive: true } }),

        // 性别统计
        ctx.db.ttsRole.groupBy({
          by: ['genderEn'],
          _count: true,
          where: { isActive: true },
        }),

        // 语言统计
        ctx.db.roleLanguageSupport.groupBy({
          by: ['languageCode'],
          _count: true,
          where: {
            role: { isActive: true },
          },
        }),

        // 提供商统计
        ctx.db.roleModelMapping.groupBy({
          by: ['modelId'],
          _count: true,
          where: {
            isActive: true,
            role: { isActive: true },
          },
        }),

        // 风格统计 - 这个需要特殊处理
        ctx.db.ttsRole.findMany({
          where: { isActive: true },
          select: { styles: true },
        }),
      ]);

      // 处理风格统计
      const styleCount: Record<string, number> = {};
      styleStats.forEach(role => {
        role.styles.forEach(style => {
          styleCount[style] = (styleCount[style] || 0) + 1;
        });
      });

      return {
        totalRoles,
        activeRoles,
        inactiveRoles: totalRoles - activeRoles,
        genderStats: genderStats.map(stat => ({
          gender: stat.genderEn || 'Unknown',
          count: stat._count,
        })),
        languageStats: languageStats.map(stat => ({
          languageCode: stat.languageCode,
          count: stat._count,
        })),
        providerStats,
        styleStats: Object.entries(styleCount).map(([style, count]) => ({
          style,
          count,
        })).sort((a, b) => b.count - a.count),
      };
    }),

  // 获取单个语音角色
  getVoiceRole: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: input.id },
      });

      if (!role) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      return role;
    }),

  // 创建语音角色
  createVoiceRole: adminProcedure
    .input(
      z.object({
        name: z.string().min(1, "名称不能为空"),
        description: z.string().min(1, "描述不能为空"),
        gender: z.enum(["MALE", "FEMALE", "NEUTRAL"]),
        ageRange: z.string().min(1, "年龄范围不能为空"),
        language: z.string().min(1, "语言不能为空"),
        accent: z.string().optional(),
        voiceCharacteristics: z.string().min(1, "声音特征不能为空"),
        isActive: z.boolean().default(true),
        avatarUrl: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // 检查名称是否已存在
      const existingRole = await ctx.db.ttsRole.findFirst({
        where: { name: input.name },
      });

      if (existingRole) {
        throw new TRPCError({ code: "CONFLICT", message: "语音角色名称已存在" });
      }

      return ctx.db.ttsRole.create({
        data: {
          name: input.name,
          description: input.description,
          slug: input.name.toLowerCase().replace(/\s+/g, '-'),
          voiceName: input.name,
          styles: [input.voiceCharacteristics],
          avatarUrl: input.avatarUrl,
          isActive: input.isActive,
        },
      });
    }),

  // 更新语音角色
  updateVoiceRole: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1, "名称不能为空"),
        description: z.string().min(1, "描述不能为空"),
        gender: z.enum(["MALE", "FEMALE", "NEUTRAL"]),
        ageRange: z.string().min(1, "年龄范围不能为空"),
        language: z.string().min(1, "语言不能为空"),
        accent: z.string().optional(),
        voiceCharacteristics: z.string().min(1, "声音特征不能为空"),
        isActive: z.boolean(),
        avatarUrl: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // 检查角色是否存在
      const existingRole = await ctx.db.ttsRole.findUnique({
        where: { id },
      });

      if (!existingRole) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      // 检查名称是否与其他角色冲突
      if (data.name !== existingRole.name) {
        const nameConflict = await ctx.db.ttsRole.findFirst({
          where: { name: data.name, id: { not: id } },
        });

        if (nameConflict) {
          throw new TRPCError({ code: "CONFLICT", message: "语音角色名称已存在" });
        }
      }

      return ctx.db.ttsRole.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          slug: data.name.toLowerCase().replace(/\s+/g, '-'),
          voiceName: data.name,
          styles: [data.voiceCharacteristics],
          avatarUrl: data.avatarUrl,
          isActive: data.isActive,
        },
      });
    }),

  // 删除语音角色
  deleteVoiceRole: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: input.id },
      });

      if (!role) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      return ctx.db.ttsRole.delete({
        where: { id: input.id },
      });
    }),

  // 切换语音角色状态
  toggleVoiceRoleStatus: adminProcedure
    .input(
      z.object({
        id: z.string(),
        isActive: z.boolean(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const role = await ctx.db.ttsRole.findUnique({
        where: { id: input.id },
      });

      if (!role) {
        throw new TRPCError({ code: "NOT_FOUND", message: "语音角色不存在" });
      }

      return ctx.db.ttsRole.update({
        where: { id: input.id },
        data: { isActive: input.isActive },
      });
    }),
});