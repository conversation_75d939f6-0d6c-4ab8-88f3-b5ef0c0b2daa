@import "tailwindcss";
@import "tw-animate-css";

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* ========================================
   从 tweakcn.com 直接复制主题 - 无需修改
   ======================================== */

:root {
  --background: oklch(0.9911 0 0);
  --foreground: oklch(0.2046 0 0);
  --card: oklch(0.9911 0 0);
  --card-foreground: oklch(0.2046 0 0);
  --popover: oklch(0.9911 0 0);
  --popover-foreground: oklch(0.4386 0 0);
  --primary: oklch(0.6270 0.1940 149.2140);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.5410 0.2810 293.0090);
  --secondary-foreground: oklch(0.9590 0.0054 17.2510);
  --muted: oklch(0.9461 0 0);
  --muted-foreground: oklch(0.2435 0 0);
  --accent: oklch(0.9461 0 0);
  --accent-foreground: oklch(0.2435 0 0);
  --destructive: oklch(0.7299 0.2310 144.1483);
  --destructive-foreground: oklch(0.9969 0.0050 145.5376);
  --border: oklch(0.9037 0 0);
  --input: oklch(0.9731 0 0);
  --ring: oklch(0.8710 0.1500 154.4490);
  --radius: 0.725rem;
  --chart-1: oklch(0.6605 0.1702 306.4132);
  --chart-2: oklch(0.6537 0.2255 3.3664);
  --chart-3: oklch(0.7433 0.1443 43.7549);
  --chart-4: oklch(0.8554 0.1969 158.6115);
  --chart-5: oklch(0.4715 0.2303 309.2911);
  --sidebar: oklch(0.9911 0 0);
  --sidebar-foreground: oklch(0.5452 0 0);
  --sidebar-primary: oklch(0.6605 0.1702 306.4132);
  --sidebar-primary-foreground: oklch(0.2446 0.0183 309.9748);
  --sidebar-accent: oklch(0.9461 0 0);
  --sidebar-accent-foreground: oklch(0.2435 0 0);
  --sidebar-border: oklch(0.9037 0 0);
  --sidebar-ring: oklch(0.6605 0.1702 306.4132);
  --sidebar-width: 16rem;
  --sidebar-width-icon: 3rem;
  --font-sans: Noto Sans Khmer, ui-sans-serif, sans-serif, system-ui;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --shadow-color: #000000;
  --shadow-opacity: 0;
  --shadow-blur: 2.5px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --shadow-xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --shadow-sm: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow-md: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 2px 4px -1px hsl(0 0% 0% / 0.00);
  --shadow-lg: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 4px 6px -1px hsl(0 0% 0% / 0.00);
  --shadow-xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 8px 10px -1px hsl(0 0% 0% / 0.00);
  --shadow-2xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --tracking-normal: 0em;
  --color-1: oklch(66.2% 0.225 25.9);
  --color-2: oklch(60.4% 0.26 302);
  --color-3: oklch(69.6% 0.165 251);
  --color-4: oklch(80.2% 0.134 225);
  --color-5: oklch(90.7% 0.231 133);
}

.dark {
  --background: oklch(0.1822 0 0);
  --foreground: oklch(0.9256 0.0172 349.5095);
  --card: oklch(0.2046 0 0);
  --card-foreground: oklch(0.9256 0.0172 349.5095);
  --popover: oklch(0.2603 0 0);
  --popover-foreground: oklch(0.7348 0 0);
  --primary: oklch(0.6270 0.1940 149.2140);
  --primary-foreground: oklch(0.9055 0.0161 310.0916);
  --secondary: oklch(0.5110 0.2620 276.9660);
  --secondary-foreground: oklch(0.9851 0 0);
  --muted: oklch(0.2393 0 0);
  --muted-foreground: oklch(0.7122 0 0);
  --accent: oklch(0.3132 0 0);
  --accent-foreground: oklch(0.9851 0 0);
  --destructive: oklch(0.3930 0.1079 144.8278);
  --destructive-foreground: oklch(0.9412 0.0075 151.8855);
  --border: oklch(0.2809 0 0);
  --input: oklch(0.2603 0 0);
  --ring: oklch(0.5960 0.1450 163.2250);
  --chart-1: oklch(0.5521 0.2131 295.2587);
  --chart-2: oklch(0.7058 0.1984 355.6083);
  --chart-3: oklch(0.8031 0.1062 39.4069);
  --chart-4: oklch(0.8792 0.1750 167.1253);
  --chart-5: oklch(0.5949 0.2518 322.1877);
  --radius: 0.725rem;
  --sidebar: oklch(0.1822 0 0);
  --sidebar-foreground: oklch(0.6301 0 0);
  --sidebar-primary: oklch(0.2799 0.1470 303.0001);
  --sidebar-primary-foreground: oklch(0.9055 0.0161 310.0916);
  --sidebar-accent: oklch(0.3132 0 0);
  --sidebar-accent-foreground: oklch(0.9851 0 0);
  --sidebar-border: oklch(0.2809 0 0);
  --sidebar-ring: oklch(0.5521 0.2131 295.2587);
  --sidebar-width: 16rem;
  --sidebar-width-icon: 3rem;
  --font-sans: Noto Sans Khmer, ui-sans-serif, sans-serif, system-ui;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: monospace;
  --shadow-color: #000000;
  --shadow-opacity: 0;
  --shadow-blur: 2.5px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --shadow-xs: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --shadow-sm: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow-md: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 2px 4px -1px hsl(0 0% 0% / 0.00);
  --shadow-lg: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 4px 6px -1px hsl(0 0% 0% / 0.00);
  --shadow-xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00), 0px 8px 10px -1px hsl(0 0% 0% / 0.00);
  --shadow-2xl: 0px 1px 2.5px 0px hsl(0 0% 0% / 0.00);
  --color-1: oklch(66.2% 0.225 25.9);
  --color-2: oklch(60.4% 0.26 302);
  --color-3: oklch(69.6% 0.165 251);
  --color-4: oklch(80.2% 0.134 225);
  --color-5: oklch(90.7% 0.231 133);
}

@theme inline {
  --font-sans: Noto Sans Khmer, ui-sans-serif, sans-serif, system-ui;
  --font-mono: monospace;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --radius: 0.725rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-foreground: var(--foreground);
  --color-background: var(--background);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --color-color-5: var(--color-5);
  --color-color-4: var(--color-4);
  --color-color-3: var(--color-3);
  --color-color-2: var(--color-2);
  --color-color-1: var(--color-1);
  @keyframes rainbow {
  0% {
    background-position: 0%;
    }
  100% {
    background-position: 200%;
    }
  }
  --animate-shine: shine var(--duration) infinite linear
;
  @keyframes shine {
  0% {
    background-position: 0% 0%;
    }
  50% {
    background-position: 100% 100%;
    }
  to {
    background-position: 0% 0%;
    }
  }
  --animate-gradient: gradient 8s linear infinite;
  @keyframes gradient {
  to {
    background-position: var(--bg-size, 300%) 0;
    }
  }}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}